import {
  Box,
  CircularProgress,
  ClickAwayListener,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Popper,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import {DebouncedInput, Table} from 'Components/Table';
import {useSnackbar} from 'notistack';
import React, {useCallback, useEffect, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';

import AddIcon from '@mui/icons-material/Add';
import {CellContext} from '@tanstack/react-table';
import ActivateIcon from 'Assets/ActivateIcon';
import DotIcon from 'Assets/DotIcon';
import EditIcon from 'Assets/EditIcon';
import ObservabilityIcon from 'Assets/ObservabilityIcon';
import SearchIcon from 'Assets/search-icon.svg';
import FilterIcon from 'Assets/tenant-filter-icon.svg';
import BlueButton from 'Components/BlueButton/BlueButton';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {
  useDeProvisioningMutation,
  useGetTenantsCountQuery,
  useGetTenantsQuery,
  useLazyExportAllTenantsQuery,
  useReActivateTenantMutation,
  useReSendInviteMutation,
  useReTriggerMutation,
  useRetryProvisioningMutation,
} from 'redux/app/tenantManagementApiSlice';

import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import SendIcon from '@mui/icons-material/Send';
import DownloadIcon from 'Assets/DownloadIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Filter, {IFilter} from 'Components/Filter/Filter';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import PermissionWrapper from 'Components/PermissionWrapper';
import {useTableState} from 'Components/Table/hook/TableStateHook';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {renderFilterButton} from 'Pages/utils';
import {EventTypes, TenantApiDTO, TenantApiForCountDTO} from 'redux/app/types';
import {
  actionStyles,
  bodyCellProps,
  coloumnCellProps,
  headerBoxStyle,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
  toolTipStyles,
} from 'styles/pages/TenantPage.styles';
import FilterStatusChips from './StatusFilterChip/StatusFilterChip';
import {getBackendColumnName, TenantStatus, tenantTableColumns} from './tenants.utils';
interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
  refetchTenants: () => void;
}
/**
 * Determines whether the dot icon should be shown based on user permission and tenant status.
 *
 * @param {boolean} hasPermission - Indicates if the user has the required permission.
 * @param {TenantStatus} tenantStatus - The current status of the tenant.
 * @returns {boolean} True if the dot icon should be displayed, false otherwise.
 */
export const shouldShowDotIconHelper = (hasPermission: boolean, tenantStatus: TenantStatus) =>
  hasPermission && tenantStatus !== TenantStatus.PENDINGONBOARDING;

const whiteMain = 'white.main';

/**
 * Returns a style object for the color property based on whether an action is disabled.
 *
 * @param {boolean} isActionDisabled - Flag to indicate if the action is disabled.
 * @returns {{color: string} | {}} An object with the `color` property when disabled, or an empty object otherwise.
 */
export const getColor = (isActionDisabled: boolean) => (isActionDisabled ? {color: 'grey.400'} : {});

/**
 * Renders action buttons for each tenant row, including "View details", "Edit", and a conditional dot menu
 * with additional actions based on the tenant's status.
 *
 * - The dot menu is shown only if the tenant status is not `PENDINGONBOARDING`.
 * - The menu contains "Observability" and either "Activate tenant" (if status is `INACTIVE`)
 *   or "Deactivate" (otherwise).
 * - Clicking the dot icon toggles the menu; clicking a menu item logs the action but does not close the menu.
 *
 * @param {IActionButtonsProps} props - The props for the action buttons component, including the row data.
 * @returns {JSX.Element} The rendered action buttons for the tenant row.
 */
export const ActionButtons: React.FC<IActionButtonsProps> = ({row, refetchTenants}) => {
  const {hasPermission} = usePermissions();

  const [anchorEl, setAnchorEl] = React.useState<null | SVGSVGElement>(null);
  const [open, setOpen] = React.useState(false);
  const navigate = useNavigate();
  const {enqueueSnackbar} = useSnackbar();
  const [loadingButton, setLoadingButton] = useState<string | null>(null);

  const [retryProvisioning] = useRetryProvisioningMutation();
  const [deProvisioning] = useDeProvisioningMutation();
  const [reSendInvite] = useReSendInviteMutation();
  const [reActivateTenant] = useReActivateTenantMutation();
  const [reTrigger] = useReTriggerMutation();

  const handleRedirectToDetails = (tenantId: string) => {
    navigate(`/tenants/${tenantId}`);
  };

  const retryProvisionTenant = 'Retry Provisioning tenant';
  const resendInviteAgain = 'Send invite again';
  const retryMigration = 'Retry Migration';
  const retryReactivation = 'Retry Reactivation';
  const retrySuspension = 'Retry Suspension';
  const reTryDeprovision = 'Retry Deprovision';
  const cancelSubscription = 'Cancel subscription';
  const handleDotClick = (event: React.MouseEvent<SVGSVGElement>) => {
    if (isActionDisabled) return;
    setAnchorEl(event.currentTarget);
    setOpen(!open);
  };

  const handleReactivate = async (data: {id: string; name: string}) => {
    try {
      setLoadingButton('Reactivate'); // Track which button is loading
      await reActivateTenant({tenantId: data.id}).unwrap();
      refetchTenants();
      enqueueSnackbar('Reactivated tenant successfully!', {variant: 'success', subMessage: data.name});
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to reactivate tenant', {variant: 'error'});
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };

  const handleRetryProvisioning = async (data: {id: string; name: string}) => {
    try {
      setLoadingButton(retryProvisionTenant); // Track which button is loading
      await retryProvisioning({tenantId: data.id}).unwrap();
      refetchTenants();
      enqueueSnackbar('Provisioning retried successfully!', {variant: 'success', subMessage: data.name});
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to retry provisioning', {variant: 'error'});
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };

  const handleDeProvisioning = async (data: {id: string; name: string}) => {
    // Don't close the menu here - only close when dot icon is clicked again
    try {
      setLoadingButton(cancelSubscription); // Track which button is loading
      await deProvisioning({tenantId: data.id}).unwrap();
      refetchTenants();
      enqueueSnackbar('Tenant canceled successfully!', {variant: 'success', subMessage: data.name});
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to cancel tenant', {variant: 'error'});
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };

  // Get the tenant status from the row data
  const tenantStatus = (row.row.original as {status: TenantStatus}).status;
  // Determine if actions should be disabled based on tenant status
  const isActionDisabled = [
    TenantStatus.PROVISIONING,
    TenantStatus.DEPROVISIONING,
    TenantStatus.SUSPENDING,
    TenantStatus.REACTIVATING,
    TenantStatus.MIGRATING,
    TenantStatus.PENDINGREACTIVATION,
  ].includes(tenantStatus);
  const shouldShowDotIcon = shouldShowDotIconHelper(hasPermission(PermissionKey.UpdateTenant), tenantStatus);

  // Menu items array - conditional based on tenant status
  const menuItems = [
    {
      label: 'Observability',
      action: 'Observability',
      icon: <ObservabilityIcon sx={{fontSize: 16, color: whiteMain}} />,
      onclick: () => {},
    },
  ];

  if (
    ![
      TenantStatus.INACTIVE,
      TenantStatus.DEPROVISIONING,
      TenantStatus.DEPROVISIONFAILED,
      TenantStatus.PENDINGPROVISIONEXPIRED,
    ].includes(tenantStatus)
  ) {
    menuItems.push({
      label: cancelSubscription,
      action: cancelSubscription,
      icon: <CancelOutlinedIcon sx={{fontSize: 16}} />,
      onclick: () => {
        handleDeProvisioning(row.row.original as {id: string; name: string});
      },
    });
  }

  /**
   * Handles retrying a tenant migration by triggering a migration event
   * for the given tenant. Displays success or error notifications and
   * manages loading state for the retry button.
   *
   * @param {Object} data - Information about the tenant.
   * @param {string} data.id - The unique identifier of the tenant whose migration should be retried.
   * @param {string} data.name - The display name of the tenant (used in the success notification).
   *
   * @returns {Promise<void>} A promise that resolves once the retry process completes.
   *
   * @example
   * await handleRetryMigration({ id: "tenant123", name: "Tenant ABC" });
   */
  const handleRetryMigration = async (data: {id: string; name: string}) => {
    try {
      setLoadingButton(retryMigration); // Track which button is loading

      await reTrigger({
        tenantId: data.id,
        body: {
          detailType: EventTypes.TENANT_MIGRATION,
          payload: {},
        },
      }).unwrap();

      refetchTenants();
      enqueueSnackbar('Migration retried successfully!', {
        variant: 'success',
        subMessage: data.name,
      });
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to retry migration', {variant: 'error'});
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };
  /**
   * Handles retrying the tenant reactivation process.
   *
   * @async
   * @function handleRetryReactivation
   * @param {{id: string, name: string}} data - The tenant data containing `id` and `name`.
   * @returns {Promise<void>} A promise that resolves when the reactivation retry process completes.
   *
   * @description
   * - Sets the loading state for the retry button.
   * - Sends a reactivation event via `reTrigger`.
   * - Refetches tenants and shows a success notification if successful.
   * - Displays an error notification if the retry fails.
   * - Resets the loading state in the `finally` block.
   */
  const handleRetryReactivation = async (data: {id: string; name: string}) => {
    try {
      setLoadingButton(retryReactivation); // Track which button is loading

      await reTrigger({
        tenantId: data.id,
        body: {
          detailType: EventTypes.TENANT_REACTIVATION,
          payload: {},
        },
      }).unwrap();

      refetchTenants();
      enqueueSnackbar('Reactivation retried successfully!', {
        variant: 'success',
        subMessage: data.name,
      });
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to retry reactivation', {
        variant: 'error',
      });
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };

  /**
   * Handles retrying the tenant suspension process.
   *
   * @async
   * @function handleRetrySuspension
   * @param {{id: string, name: string}} data - The tenant data containing `id` and `name`.
   * @returns {Promise<void>} A promise that resolves when the suspension retry process completes.
   *
   * @description
   * - Sets the loading state for the retry button.
   * - Sends a suspension event via `reTrigger`.
   * - Refetches tenants and shows a success notification if successful.
   * - Displays an error notification if the retry fails.
   * - Resets the loading state in the `finally` block.
   */
  const handleRetrySuspension = async (data: {id: string; name: string}) => {
    try {
      setLoadingButton(retrySuspension); // Track which button is loading

      await reTrigger({
        tenantId: data.id,
        body: {
          detailType: EventTypes.TENANT_SUSPENSION,
          payload: {},
        },
      }).unwrap();

      refetchTenants();
      enqueueSnackbar('Suspension retried successfully!', {
        variant: 'success',
        subMessage: data.name,
      });
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to retry suspension', {
        variant: 'error',
      });
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };

  const handleSendInviteAgain = async (data: {id: string; name: string}) => {
    // Don't close the menu here - only close when dot icon is clicked again
    try {
      setLoadingButton(resendInviteAgain); // Track which button is loading
      await reSendInvite({tenantId: data.id}).unwrap();
      refetchTenants();
      enqueueSnackbar('Tenant invite sent successfully!', {variant: 'success', subMessage: data.name});
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to send invite', {variant: 'error'});
    } finally {
      setLoadingButton(null); // Reset loading state
    }
  };

  const handleReTryDeProvisioning = async (data: {id: string; name: string}) => {
    try {
      setLoadingButton(reTryDeprovision);
      await reTrigger({
        tenantId: data.id,
        body: {
          detailType: EventTypes.TENANT_DEPROVISIONING,
          payload: {},
        },
      }).unwrap();
      refetchTenants();
      enqueueSnackbar('Deprovisioning retried successfully!', {variant: 'success', subMessage: data.name});
    } catch (error) {
      enqueueSnackbar((error as {message?: string})?.message || 'Failed to retry deprovisioning', {variant: 'error'});
    } finally {
      setLoadingButton(null);
    }
  };

  const handleEditTenant = (tenantId: string) => {
    navigate(`/tenants/${tenantId}/edit`);
  };

  switch (tenantStatus) {
    case TenantStatus.INACTIVE:
      menuItems.push({
        label: 'Reactivate',
        action: 'Reactivate',
        icon: <ActivateIcon sx={{fontSize: 16, color: whiteMain}} />,
        onclick: () => {
          handleReactivate(row.row.original as {id: string; name: string});
        },
      });
      break;

    case TenantStatus.PROVISIONFAILED:
      menuItems.push({
        label: retryProvisionTenant,
        action: retryProvisionTenant,
        icon: <ActivateIcon sx={{fontSize: 16, color: whiteMain}} />,
        onclick: () => {
          handleRetryProvisioning(row.row.original as {id: string; name: string});
        },
      });
      break;

    case TenantStatus.MIGRATIONFAILED:
      menuItems.push({
        label: retryMigration,
        action: retryMigration,
        icon: <ActivateIcon sx={{fontSize: 16, color: whiteMain}} />,
        onclick: () => {
          handleRetryMigration(row.row.original as {id: string; name: string});
        },
      });
      break;

    case TenantStatus.REACTIVATIONFAILED:
      menuItems.push({
        label: retryReactivation,
        action: retryReactivation,
        icon: <ActivateIcon sx={{fontSize: 16, color: whiteMain}} />,
        onclick: () => {
          handleRetryReactivation(row.row.original as {id: string; name: string});
        },
      });
      break;

    case TenantStatus.SUSPENSIONFAILED:
      menuItems.push({
        label: retrySuspension,
        action: retrySuspension,
        icon: <ActivateIcon sx={{fontSize: 16, color: whiteMain}} />,
        onclick: () => {
          handleRetrySuspension(row.row.original as {id: string; name: string});
        },
      });
      break;

    case TenantStatus.PENDINGPROVISIONEXPIRED:
      menuItems.push({
        label: resendInviteAgain,
        action: resendInviteAgain,
        icon: <SendIcon sx={{fontSize: 16}} />,
        onclick: () => {
          handleSendInviteAgain(row.row.original as {id: string; name: string});
        },
      });
      break;

    case TenantStatus.DEPROVISIONFAILED:
      menuItems.push({
        label: reTryDeprovision,
        action: reTryDeprovision,
        icon: <CancelOutlinedIcon sx={{fontSize: 16}} />,
        onclick: () => {
          handleReTryDeProvisioning(row.row.original as {id: string; name: string});
        },
      });
      break;

    default:
      break;
  }

  return (
    <Stack display="flex" flexDirection={'row'}>
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon sx={actionStyles} onClick={() => handleRedirectToDetails((row.row.original as {id: string}).id)} />
      </Tooltip>
      <PermissionWrapper permission={PermissionKey.UpdateTenant}>
        <Tooltip title="Edit" placement="top" arrow slotProps={toolTipStyles}>
          <EditIcon
            onClick={() => !isActionDisabled && handleEditTenant((row.row.original as {id: string}).id)}
            sx={{
              ...actionStyles,
              ...getColor(isActionDisabled),
            }}
            data-testid="edit-button"
          />
        </Tooltip>
      </PermissionWrapper>
      {shouldShowDotIcon && (
        <>
          <DotIcon
            sx={{...actionStyles, ...getColor(isActionDisabled)}}
            onClick={handleDotClick}
            data-testid="dot-icon"
          />
          <Popper
            open={open}
            anchorEl={anchorEl}
            placement="bottom-end"
            disablePortal={false}
            modifiers={[
              {
                name: 'offset',
                options: {
                  offset: [0, 8],
                },
              },
            ]}
          >
            <ClickAwayListener onClickAway={() => setOpen(false)}>
              <Paper
                sx={{
                  borderWidth: '0.0625rem',
                  borderStyle: 'solid',
                  borderColor: 'divider',
                  boxShadow: theme => `0 0.25rem 0.5rem ${theme.palette.body[Integers.OneHundred]}`,
                  minWidth: 120,
                }}
              >
                <List sx={{py: 0}}>
                  {menuItems.map(item => (
                    <ListItemButton
                      key={item.action}
                      onClick={item.onclick}
                      sx={{
                        py: 0.75,
                        px: 2.5,
                        borderBottom: theme => `0.0625rem solid ${theme.palette.body[Integers.Fifty]}`,
                        color: 'body.dark',
                        '&:hover': {backgroundColor: whiteMain},
                      }}
                    >
                      <ListItemIcon sx={{minWidth: 32}}>
                        {loadingButton === item.label ? <CircularProgress size={16} /> : item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.label}
                        sx={{
                          '& .MuiTypography-root': {
                            fontSize: '0.8125rem',
                            fontWeight: 600,
                            color: 'body.dark',
                          },
                        }}
                      />
                    </ListItemButton>
                  ))}
                </List>
              </Paper>
            </ClickAwayListener>
          </Popper>
        </>
      )}
    </Stack>
  );
};
const buttonHeight = '2.375rem';

/**
 * Tenant page component for displaying and managing tenants.
 *
 * This component provides a paginated, sortable, and searchable table of tenants.
 * It fetches tenant data and count from the backend using RTK Query hooks,
 * and displays error notifications using notistack's snackbar.
 *
 * Features:
 * - Search tenants by name.
 * - Sort tenants by selectable columns.
 * - Paginate through tenants with adjustable page size.
 * - Add new tenants via navigation.
 * - Displays loading indicators during data fetch.
 *
 * @component
 * @returns {JSX.Element} The rendered tenant management page.
 */
const Tenant: React.FC = () => {
  const {enqueueSnackbar} = useSnackbar();
  const {limit, setLimit, offset, setOffset, handlePageChange, handleRowsPerPageChange} = useTableState();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('createdOn DESC');

  // Initial sort state for the table - use frontend column ID
  const initialSortState = [{id: 'createdDate', desc: true}];

  const [selectedITenantFilter, setSelectedITenantFilter] = React.useState<IFilter | undefined>(undefined);
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const [openFilter, setOpenFilter] = React.useState(false);
  const location = useLocation();

  const handleSortChange = (columnId: string, sort: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${sort ? 'DESC' : 'ASC'}`;
    setOffset(0);
    setSortBy(sortParam);
  };

  React.useEffect(() => {
    setOffset(0);
  }, [searchTerm]);

  // Build the filter object for the API call
  const filterParams: TenantApiDTO = {
    limit,
    offset,
    order: sortBy,
    searchValue: searchTerm,
    status: selectedITenantFilter?.status ? Array.from(selectedITenantFilter.status).map(Number) : undefined,
    dateRange: selectedITenantFilter?.dateRange,
  };

  // Build count filter (without limit/offset)
  const countFilterParams: TenantApiForCountDTO = {
    order: sortBy,
    searchValue: searchTerm,
    status: selectedITenantFilter?.status ? Array.from(selectedITenantFilter.status).map(Number) : undefined,
    dateRange: selectedITenantFilter?.dateRange,
  };

  const {
    data: tenants,
    error: tenantsError,
    isFetching: isLoading,
    refetch: refetchTenants,
  } = useGetTenantsQuery(filterParams, {
    refetchOnMountOrArgChange: true,
  });
  const {
    data: tenantsCount,
    error: countError,
    isFetching: countLoading,
  } = useGetTenantsCountQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
  });

  const [getExportAllTenants, {isFetching: isExporting}] = useLazyExportAllTenantsQuery();

  const handleExportAll = useCallback(async () => {
    try {
      const result = await getExportAllTenants().unwrap();
      const {downloadUrl} = result;
      window.open(downloadUrl, '_blank');
    } catch (error) {
      if ((error as {status: number}).status === Integers.FourHundredFour) {
        enqueueSnackbar('No Tenant to export', {variant: 'error'});
        return;
      }
      enqueueSnackbar('Failed to export tenants', {variant: 'error'});
    }
  }, [enqueueSnackbar, getExportAllTenants]);

  useEffect(() => {
    if (!location?.state) return;
    if ('statusFilter' in location.state) {
      setSelectedITenantFilter({
        status: location.state['statusFilter'] as Set<string>,
        dateRange: undefined,
      });
    }
  }, [location]);

  // Show error notifications
  React.useEffect(() => {
    if (tenantsError) {
      enqueueSnackbar('Failed to fetch tenants data', {variant: 'error'});
    }
    if (countError) {
      enqueueSnackbar('Failed to fetch tenants count', {variant: 'error'});
    }
  }, [tenantsError, countError, enqueueSnackbar]);
  // Trigger initial sort on mount
  React.useEffect(() => {
    handleSortChange('createdDate', true);
  }, []); // Empty dependency array means this runs once on mount

  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate('/add-tenant');
  };

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        {/* Search Input */}
        <DebouncedInput
          placeholder="Search tenant name"
          data-testid="search-tenant"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'black.main',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />

        <DefaultToolTip title="Export all tenants" placement="top" arrow>
          <BorderButton sx={{minWidth: 0, px: 1.5}} loading={isExporting} onClick={handleExportAll}>
            <DownloadIcon sx={{color: 'transparent', width: 20}} />
          </BorderButton>
        </DefaultToolTip>

        {/* // Filter Button */}
        {renderFilterButton({
          filterButtonRef,
          buttonHeight,
          filterSize: (selectedITenantFilter?.status?.size ?? 0) + (selectedITenantFilter?.dateRange ? 1 : 0),
          hasFilters: !!((selectedITenantFilter?.status?.size ?? 0) + (selectedITenantFilter?.dateRange ? 1 : 0)),
          setOpenFilter,
          FilterIcon,
          BorderButton,
          SVGImageFromPath,
        })}

        {/* // Add Tenant Button */}
        <PermissionWrapper permission={PermissionKey.CreateTenant}>
          <BlueButton
            sx={{height: buttonHeight, fontSize: '0.8125rem', fontWeight: 700, pl: 2, pr: 2.5}}
            onClick={handleRedirect}
          >
            <Box sx={{flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1}}>
              <AddIcon sx={{height: '1rem', width: '1rem'}} />
              Add Tenant
            </Box>
          </BlueButton>
        </PermissionWrapper>
      </Box>
    );
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          Tenants
        </Typography>
        {buildTopRightSection()}
      </Box>
      <Box sx={{position: 'relative', minHeight: '200px'}}>
        <Table
          data={tenants || []}
          columns={tenantTableColumns(refetchTenants)}
          enableSorting={true}
          initialSortingState={initialSortState}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={tenantsCount?.count || 0}
          manualPagination={true}
          onSortChange={handleSortChange}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="tenant-table"
        />
        {(isLoading || countLoading) && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255, 255, 255, 0.7)',
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        )}
      </Box>
      {/* Tenant Filter */}
      <Filter
        open={openFilter}
        value={selectedITenantFilter}
        onClose={() => setOpenFilter(false)}
        anchorEl={filterButtonRef.current}
        onFilterChange={filter => {
          setOffset(0);
          setSelectedITenantFilter(filter);
        }}
        FilterStatusChips={FilterStatusChips}
      />
    </Box>
  );
};

export default Tenant;
