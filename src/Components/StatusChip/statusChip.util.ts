const whiteMain = 'white.main';

/**
 * Enumeration for the different states of the status chip.
 */
export enum StatusChipState {
  ACTIVE, // Tenant is active and fully functional
  PENDINGPROVISION, // Tenant is awaiting provisioning
  PROVISIONING, // Tenant is currently being provisioned
  PROVISIONFAILED, // Provisioning process failed
  INACTIVE, // Tenant is inactive
  PENDINGONBOARDING, // Tenant is active but not onboarded due to missing information,
  TENANTSUSPENDED,
  TENANTSUSPENDEDOVERDUE,
  PENDINGPROVISIONEXPIRED,
  MIGRATING,
  MIGRATIONFAILED,
  DEPROVISIONING,
  DEPROVISIONFAILED,
  SUSPENDING,
  SUSPENSIONFAILED,
  REACTIVATING,
  REA<PERSON><PERSON>VA<PERSON>ONFAILED,
  PENDING_REACTIVATION,
}
/**
 * Get the background color for a given status.
 * @param status - The status of the chip.
 * @returns The background color path as a string.
 */
// ----------------- Status Mapping Functions -----------------

export const getStatusColor = (status: StatusChipState): string => {
  const map: Record<StatusChipState, string> = {
    [StatusChipState.ACTIVE]: 'alert.success.bg',
    [StatusChipState.PENDINGPROVISION]: 'alert.warning.bg',
    [StatusChipState.INACTIVE]: 'alert.error.bg',
    [StatusChipState.PENDINGONBOARDING]: 'alert.info.bg',
    [StatusChipState.MIGRATING]: 'tenantStatus.migrating.bg',
    [StatusChipState.PROVISIONFAILED]: 'tenantStatus.failed.bg',
    [StatusChipState.PROVISIONING]: 'tenantStatus.provisioning.bg',
    [StatusChipState.TENANTSUSPENDED]: 'tenantStatus.suspended.bg',
    [StatusChipState.TENANTSUSPENDEDOVERDUE]: 'tenantStatus.suspendedOverdue.bg',
    [StatusChipState.PENDINGPROVISIONEXPIRED]: 'tenantStatus.pendingProvisioningExpired.bg',
    [StatusChipState.MIGRATIONFAILED]: 'tenantStatus.migrationFailed.bg',
    [StatusChipState.DEPROVISIONING]: 'tenantStatus.deprovisioning.bg',
    [StatusChipState.DEPROVISIONFAILED]: 'tenantStatus.deprovisionFailed.bg',
    [StatusChipState.SUSPENDING]: 'tenantStatus.suspending.bg',
    [StatusChipState.SUSPENSIONFAILED]: 'tenantStatus.suspensionFailed.bg',
    [StatusChipState.REACTIVATING]: 'tenantStatus.reactivating.bg',
    [StatusChipState.REACTIVATIONFAILED]: 'tenantStatus.reactivationFailed.bg',
    [StatusChipState.PENDING_REACTIVATION]: 'tenantStatus.pendingReactivation.bg',
  };
  return map[status] ?? whiteMain;
};

export const getFontColor = (status: StatusChipState): string => {
  const map: Record<StatusChipState, string> = {
    [StatusChipState.ACTIVE]: 'alert.success.onBg',
    [StatusChipState.PENDINGPROVISION]: 'alert.warning.onBg',
    [StatusChipState.INACTIVE]: 'alert.error.onBg',
    [StatusChipState.PENDINGONBOARDING]: 'alert.info.onBg',
    [StatusChipState.MIGRATING]: 'tenantStatus.migrating.onBg',
    [StatusChipState.PROVISIONFAILED]: 'tenantStatus.failed.onBg',
    [StatusChipState.PROVISIONING]: 'tenantStatus.provisioning.onBg',
    [StatusChipState.TENANTSUSPENDED]: 'tenantStatus.suspended.onBg',
    [StatusChipState.TENANTSUSPENDEDOVERDUE]: 'tenantStatus.suspendedOverdue.onBg',
    [StatusChipState.PENDINGPROVISIONEXPIRED]: 'tenantStatus.pendingProvisioningExpired.onBg',
    [StatusChipState.MIGRATIONFAILED]: 'tenantStatus.migrationFailed.onBg',
    [StatusChipState.DEPROVISIONING]: 'tenantStatus.deprovisioning.onBg',
    [StatusChipState.DEPROVISIONFAILED]: 'tenantStatus.deprovisionFailed.onBg',
    [StatusChipState.SUSPENDING]: 'tenantStatus.suspending.onBg',
    [StatusChipState.SUSPENSIONFAILED]: 'tenantStatus.suspensionFailed.onBg',
    [StatusChipState.REACTIVATING]: 'tenantStatus.reactivating.onBg',
    [StatusChipState.REACTIVATIONFAILED]: 'tenantStatus.reactivationFailed.onBg',
    [StatusChipState.PENDING_REACTIVATION]: 'tenantStatus.pendingReactivation.onBg',
  };
  return map[status] ?? whiteMain;
};

export const getIndicatorColor = (status: StatusChipState): string => {
  const map: Record<StatusChipState, string> = {
    [StatusChipState.ACTIVE]: 'alert.success.main',
    [StatusChipState.PENDINGPROVISION]: 'alert.warning.main',
    [StatusChipState.INACTIVE]: 'alert.error.main',
    [StatusChipState.PENDINGONBOARDING]: 'alert.info.main',
    [StatusChipState.MIGRATING]: 'tenantStatus.migrating.indicator',
    [StatusChipState.PROVISIONFAILED]: 'tenantStatus.failed.indicator',
    [StatusChipState.PROVISIONING]: 'tenantStatus.provisioning.indicator',
    [StatusChipState.TENANTSUSPENDED]: 'tenantStatus.suspended.indicator',
    [StatusChipState.TENANTSUSPENDEDOVERDUE]: 'tenantStatus.suspendedOverdue.indicator',
    [StatusChipState.PENDINGPROVISIONEXPIRED]: 'tenantStatus.pendingProvisioningExpired.indicator',
    [StatusChipState.MIGRATIONFAILED]: 'tenantStatus.migrationFailed.indicator',
    [StatusChipState.DEPROVISIONING]: 'tenantStatus.deprovisioning.indicator',
    [StatusChipState.DEPROVISIONFAILED]: 'tenantStatus.deprovisionFailed.indicator',
    [StatusChipState.SUSPENDING]: 'tenantStatus.suspending.indicator',
    [StatusChipState.SUSPENSIONFAILED]: 'tenantStatus.suspensionFailed.indicator',
    [StatusChipState.REACTIVATING]: 'tenantStatus.reactivating.indicator',
    [StatusChipState.REACTIVATIONFAILED]: 'tenantStatus.reactivationFailed.indicator',
    [StatusChipState.PENDING_REACTIVATION]: 'tenantStatus.pendingReactivation.indicator',
  };
  return map[status] ?? whiteMain;
};

/**
 * Get the status label for a given status.
 * @param status - The status of the chip.
 * @returns The status label as a string.
 */
export const getStatusLabel = (status: StatusChipState | number): string => {
  const statusLabelMap: Record<StatusChipState | number, string> = {
    [StatusChipState.ACTIVE]: 'Active',
    [StatusChipState.PENDINGPROVISION]: 'Pending Provision',
    [StatusChipState.INACTIVE]: 'Inactive',
    [StatusChipState.PROVISIONFAILED]: 'Failed Provision',
    [StatusChipState.PROVISIONING]: 'Provisioning',
    [StatusChipState.PENDINGONBOARDING]: 'Pending Onboarding',
    [StatusChipState.TENANTSUSPENDED]: 'Tenant Suspended',
    [StatusChipState.TENANTSUSPENDEDOVERDUE]: 'Tenant Suspended Overdue',
    [StatusChipState.PENDINGPROVISIONEXPIRED]: 'Pending Provision Expired',
    [StatusChipState.MIGRATING]: 'Migrating',
    [StatusChipState.MIGRATIONFAILED]: 'Migration Failed',
    [StatusChipState.DEPROVISIONING]: 'Deprovisioning',
    [StatusChipState.DEPROVISIONFAILED]: 'Deprovision Failed',
    [StatusChipState.SUSPENDING]: 'Suspending',
    [StatusChipState.SUSPENSIONFAILED]: 'Suspension Failed',
    [StatusChipState.REACTIVATING]: 'Reactivating',
    [StatusChipState.REACTIVATIONFAILED]: 'Reactivation Failed',
    [StatusChipState.PENDING_REACTIVATION]: 'Pending Reactivation',
  };
  return statusLabelMap[status] || '';
};
