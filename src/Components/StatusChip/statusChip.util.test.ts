import {testingPalette, useThemeColor} from 'TestHelper/TestHelper';
import {StatusChipState, getFontColor, getIndicatorColor, getStatusColor, getStatusLabel} from './statusChip.util';

// Mock colors import for fallback
vi.mock('Providers/theme/colors', () => ({
  colors: {white: '#FFFFFF'},
}));

const UNKNOWN_RANDOM_STATUS = 999;
const UNKNOWN_STATUS = UNKNOWN_RANDOM_STATUS as StatusChipState;
const FallbackColorForUnknownMsg = 'returns fallback color for unknown status';
describe('StatusChip Utils', () => {
  describe('getStatusColor', () => {
    it('returns correct color for each status', () => {
      expect(useThemeColor(getStatusColor(StatusChipState.ACTIVE))).toBe(testingPalette.alert.success.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.PENDINGPROVISION))).toBe(testingPalette.alert.warning.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.INACTIVE))).toBe(testingPalette.alert.error.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.PROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.failed.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.PROVISIONING))).toBe(
        testingPalette.tenantStatus.provisioning.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.PENDINGONBOARDING))).toBe(testingPalette.alert.info.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.TENANTSUSPENDED))).toBe(
        testingPalette.tenantStatus.suspended.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.TENANTSUSPENDEDOVERDUE))).toBe(
        testingPalette.tenantStatus.suspendedOverdue.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.PENDINGPROVISIONEXPIRED))).toBe(
        testingPalette.tenantStatus.pendingProvisioningExpired.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.MIGRATING))).toBe(testingPalette.tenantStatus.migrating.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.MIGRATIONFAILED))).toBe(
        testingPalette.tenantStatus.migrationFailed.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.DEPROVISIONING))).toBe(
        testingPalette.tenantStatus.deprovisioning.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.DEPROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.deprovisionFailed.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.SUSPENDING))).toBe(testingPalette.tenantStatus.suspending.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.SUSPENSIONFAILED))).toBe(
        testingPalette.tenantStatus.suspensionFailed.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.REACTIVATING))).toBe(
        testingPalette.tenantStatus.reactivating.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.REACTIVATIONFAILED))).toBe(
        testingPalette.tenantStatus.reactivationFailed.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.PENDING_REACTIVATION))).toBe(
        testingPalette.tenantStatus.pendingReactivation.bg,
      );
    });

    it(FallbackColorForUnknownMsg, () => {
      expect(useThemeColor(getStatusColor(UNKNOWN_STATUS))).toBe(testingPalette.white.main);
    });
  });

  describe('getFontColor', () => {
    it('returns correct font color for each status', () => {
      expect(useThemeColor(getFontColor(StatusChipState.ACTIVE))).toBe(testingPalette.alert.success.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.PENDINGPROVISION))).toBe(testingPalette.alert.warning.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.INACTIVE))).toBe(testingPalette.alert.error.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.PROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.failed.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.PROVISIONING))).toBe(
        testingPalette.tenantStatus.provisioning.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.PENDINGONBOARDING))).toBe(testingPalette.alert.info.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.TENANTSUSPENDED))).toBe(
        testingPalette.tenantStatus.suspended.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.TENANTSUSPENDEDOVERDUE))).toBe(
        testingPalette.tenantStatus.suspendedOverdue.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.PENDINGPROVISIONEXPIRED))).toBe(
        testingPalette.tenantStatus.pendingProvisioningExpired.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.MIGRATING))).toBe(testingPalette.tenantStatus.migrating.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.MIGRATIONFAILED))).toBe(
        testingPalette.tenantStatus.migrationFailed.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.DEPROVISIONING))).toBe(
        testingPalette.tenantStatus.deprovisioning.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.DEPROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.deprovisionFailed.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.SUSPENDING))).toBe(testingPalette.tenantStatus.suspending.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.SUSPENSIONFAILED))).toBe(
        testingPalette.tenantStatus.suspensionFailed.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.REACTIVATING))).toBe(
        testingPalette.tenantStatus.reactivating.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.REACTIVATIONFAILED))).toBe(
        testingPalette.tenantStatus.reactivationFailed.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.PENDING_REACTIVATION))).toBe(
        testingPalette.tenantStatus.pendingReactivation.onBg,
      );
    });

    it(FallbackColorForUnknownMsg, () => {
      expect(useThemeColor(getFontColor(UNKNOWN_STATUS))).toBe(testingPalette.white.main);
    });
  });

  describe('getIndicatorColor', () => {
    it('returns correct indicator color for each status', () => {
      expect(useThemeColor(getIndicatorColor(StatusChipState.ACTIVE))).toBe(testingPalette.alert.success.main);
      expect(useThemeColor(getIndicatorColor(StatusChipState.PENDINGPROVISION))).toBe(
        testingPalette.alert.warning.main,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.INACTIVE))).toBe(testingPalette.alert.error.main);
      expect(useThemeColor(getIndicatorColor(StatusChipState.PROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.failed.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.PROVISIONING))).toBe(
        testingPalette.tenantStatus.provisioning.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.PENDINGONBOARDING))).toBe(testingPalette.alert.info.main);
      expect(useThemeColor(getIndicatorColor(StatusChipState.TENANTSUSPENDED))).toBe(
        testingPalette.tenantStatus.suspended.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.TENANTSUSPENDEDOVERDUE))).toBe(
        testingPalette.tenantStatus.suspendedOverdue.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.PENDINGPROVISIONEXPIRED))).toBe(
        testingPalette.tenantStatus.pendingProvisioningExpired.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.MIGRATING))).toBe(
        testingPalette.tenantStatus.migrating.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.MIGRATIONFAILED))).toBe(
        testingPalette.tenantStatus.migrationFailed.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.DEPROVISIONING))).toBe(
        testingPalette.tenantStatus.deprovisioning.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.DEPROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.deprovisionFailed.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.SUSPENDING))).toBe(
        testingPalette.tenantStatus.suspending.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.SUSPENSIONFAILED))).toBe(
        testingPalette.tenantStatus.suspensionFailed.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.REACTIVATING))).toBe(
        testingPalette.tenantStatus.reactivating.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.REACTIVATIONFAILED))).toBe(
        testingPalette.tenantStatus.reactivationFailed.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.PENDING_REACTIVATION))).toBe(
        testingPalette.tenantStatus.pendingReactivation.indicator,
      );
    });

    it(FallbackColorForUnknownMsg, () => {
      expect(useThemeColor(getIndicatorColor(UNKNOWN_STATUS))).toBe(testingPalette.white.main);
    });
  });

  describe('getStatusLabel', () => {
    it('returns correct label for each status', () => {
      expect(getStatusLabel(StatusChipState.ACTIVE)).toBe('Active');
      expect(getStatusLabel(StatusChipState.PENDINGPROVISION)).toBe('Pending Provision');
      expect(getStatusLabel(StatusChipState.INACTIVE)).toBe('Inactive');
      expect(getStatusLabel(StatusChipState.PROVISIONFAILED)).toBe('Failed Provision');
      expect(getStatusLabel(StatusChipState.PROVISIONING)).toBe('Provisioning');
      expect(getStatusLabel(StatusChipState.PENDINGONBOARDING)).toBe('Pending Onboarding');
      expect(getStatusLabel(StatusChipState.TENANTSUSPENDED)).toBe('Tenant Suspended');
      expect(getStatusLabel(StatusChipState.TENANTSUSPENDEDOVERDUE)).toBe('Tenant Suspended Overdue');
      expect(getStatusLabel(StatusChipState.PENDINGPROVISIONEXPIRED)).toBe('Pending Provision Expired');
      expect(getStatusLabel(StatusChipState.MIGRATING)).toBe('Migrating');
      expect(getStatusLabel(StatusChipState.MIGRATIONFAILED)).toBe('Migration Failed');
      expect(getStatusLabel(StatusChipState.DEPROVISIONING)).toBe('Deprovisioning');
      expect(getStatusLabel(StatusChipState.DEPROVISIONFAILED)).toBe('Deprovision Failed');
      expect(getStatusLabel(StatusChipState.SUSPENDING)).toBe('Suspending');
      expect(getStatusLabel(StatusChipState.SUSPENSIONFAILED)).toBe('Suspension Failed');
      expect(getStatusLabel(StatusChipState.REACTIVATING)).toBe('Reactivating');
      expect(getStatusLabel(StatusChipState.REACTIVATIONFAILED)).toBe('Reactivation Failed');
      expect(getStatusLabel(StatusChipState.PENDING_REACTIVATION)).toBe('Pending Reactivation');
    });

    it('returns empty string for unknown status', () => {
      expect(getStatusLabel(UNKNOWN_STATUS)).toBe('');
    });
  });
});
